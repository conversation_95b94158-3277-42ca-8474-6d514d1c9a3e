* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

header h2 a {
  color: #3a6ea5;
  text-decoration: none;
}

.header-links {
  display: flex;
  gap: 1.5rem;
}

.header-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.header-links a:hover {
  color: #3a6ea5;
}

.logout-link {
  color: #dc3545 !important;
}

.server-msg {
  max-width: 800px;
  margin: 1rem auto;
  padding: 0 1rem;
}

.server-msg .error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.server-msg .success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

main {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.settings-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-container h1 {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #eee;
  margin-bottom: 0;
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  background-color: #f8f9fa;
}

.tab-btn {
  padding: 1rem 2rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  transition: all 0.3s;
}

.tab-btn:hover {
  background-color: #e9ecef;
}

.tab-btn.active {
  color: #3a6ea5;
  border-bottom: 3px solid #3a6ea5;
  background-color: #fff;
}

.tab-content {
  display: none;
  padding: 2rem;
}

.tab-content.active {
  display: block;
}

.tab-content h2 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.5rem;
}

.settings-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #3a6ea5;
  outline: none;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-hint {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.save-btn {
  padding: 0.75rem 1.5rem;
  background-color: #3a6ea5;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.save-btn:hover {
  background-color: #2c5282;
}

.current-photo {
  margin-bottom: 2rem;
}

.current-photo h3 {
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: #555;
}

.photo-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #ddd;
}

.photo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* File input styling */
input[type="file"] {
  border: 1px solid #ddd;
  padding: 0.5rem;
  border-radius: 4px;
  width: 100%;
}

input[type="file"]::-webkit-file-upload-button {
  background-color: #3a6ea5;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .settings-tabs {
    flex-direction: column;
  }

  .tab-btn {
    text-align: left;
    border-bottom: 1px solid #ddd;
  }

  .tab-btn.active {
    border-bottom: 1px solid #ddd;
    border-left: 3px solid #3a6ea5;
  }

  .photo-preview {
    width: 120px;
    height: 120px;
  }
}
