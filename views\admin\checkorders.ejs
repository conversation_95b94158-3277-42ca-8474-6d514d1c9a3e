<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/admin/checkorders.css" />
    <title>Check Orders</title>
  </head>
  <body>
    <header>
      <h2>Admin</h2>
      <a href="/admin/homepage" class="dashboard-btn">Back to Dashboard</a>
    </header>
    <div class="server-msg">
      <% if(hasErr){%> <% errMsg.forEach(message =>{ %>
      <p class="error"><%= message%></p>
      <% })%> <% } %> <% if(hasSuccess){%> <% successMsg.forEach(message =>{ %>
      <p class="success"><%= message%></p>
      <% })%> <% } %>
    </div>
    <main>
      <h2>Orders</h2>
      <% if(orders && orders.length > 0) { %>
        <div class="orders-container">
          <% orders.forEach(order => { %>
            <div class="order-card">
              <h3>Order #<%= order._id %></h3>
              <!-- Add order details here when you have the Order model -->
            </div>
          <% }); %>
        </div>
      <% } else { %>
        <p class="no-orders">No orders found</p>
      <% } %>
    </main>
  </body>
</html>