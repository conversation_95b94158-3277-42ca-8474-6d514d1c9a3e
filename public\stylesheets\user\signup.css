/* signup.css */

/* Reset basic styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f3e5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #333;
  height: 100vh;
}

/* Header Styling */
header {
  background-color: #8e24aa;
  color: #fff;
  padding: 1rem;
  width: 100%;
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h2 {
  margin: 0;
}

/* Server Message Styling */
.server-msg {
  margin-top: 1rem;
  width: 90%;
  max-width: 500px;
}

.server-msg p {
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
}

/* Error Message Styling */
.server-msg .error {
  background-color: #ffcdd2;
  color: #b71c1c;
}

/* Success Message Styling */
.server-msg .success {
  background-color: #c8e6c9;
  color: #2e7d32;
}

/* Main Content Styling */
main {
  margin-top: 2rem;
  width: 90%;
  max-width: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
}

main h1 {
  font-size: 1.8rem;
  color: #8e24aa;
  margin-bottom: 1.5rem;
}

/* Form Styling */
form {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1rem;
}

input[type="text"],
input[type="email"],
input[type="password"] {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  width: 100%;
}

input[type="submit"] {
  background-color: #8e24aa;
  color: #fff;
  font-size: 1rem;
  padding: 0.75rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

input[type="submit"]:hover {
  background-color: #6a1b9a;
}

/* Login Link Styling */
main h6 {
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #666;
}

main h6 a {
  color: #8e24aa;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
}

main h6 a:hover {
  color: #6a1b9a;
}

/* Responsive Design */
@media (max-width: 500px) {
  main,
  .server-msg {
    width: 100%;
  }
}
