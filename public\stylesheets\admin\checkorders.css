* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
}

/* Header Styling */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #333;
  color: white;
}

header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.dashboard-btn {
  background-color: #3a6ea5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
}

.dashboard-btn:hover {
  background-color: #2c5282;
}

/* Server Messages */
.server-msg {
  max-width: 800px;
  margin: 1rem auto;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
  border-radius: 0.25rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
  border-radius: 0.25rem;
}

/* Main Content */
main {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

main h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.no-orders {
  text-align: center;
  color: #6c757d;
  padding: 2rem 0;
}

.orders-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.order-card {
  padding: 1.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.order-card h3 {
  margin-bottom: 1rem;
  color: #333;
}

/* Responsive Design */
@media (max-width: 600px) {
  main {
    width: 100%;
    border-radius: 0;
  }
  
  .orders-container {
    grid-template-columns: 1fr;
  }
}