<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/user/checkout.css" />
    <title>Checkout | Danilo Store</title>
  </head>
  <body>
    <header>
      <h1><a href="/">Danilo Store</a></h1>
      <h2>Checkout</h2>
    </header>

    <div class="server-msg">
      <% if(hasErr){%> <% errMsg.forEach(message =>{ %>
      <p class="error"><%= message%></p>
      <% })%> <% } %> <% if(hasSuccess){%> <% successMsg.forEach(message =>{ %>
      <p class="success"><%= message%></p>
      <% })%> <% } %>
    </div>

    <div class="checkout-container">
      <div class="checkout-form">
        <h3>Shipping Information</h3>
        <form action="/user/createorder" method="post">
          <div class="form-group">
            <label for="name">Full Name</label>
            <input type="text" id="name" name="name" value="<%= user.fullname %>" required>
          </div>
          
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" value="<%= user.email %>" required>
          </div>
          
          <div class="form-group">
            <label for="address">Shipping Address</label>
            <textarea id="address" name="address" required><%= user.address || '' %></textarea>
          </div>
          
          <h3>Payment Information</h3>
          
          <div class="form-group">
            <label for="card_num">Card Number</label>
            <input type="text" id="card_num" name="card_num" placeholder="1234 5678 9012 3456" required>
          </div>
          
          <div class="form-row">
            <div class="form-group half">
              <label for="card_exp_month">Expiry Month</label>
              <input type="text" id="card_exp_month" name="card_exp_month" placeholder="MM" required>
            </div>
            
            <div class="form-group half">
              <label for="card_exp_year">Expiry Year</label>
              <input type="text" id="card_exp_year" name="card_exp_year" placeholder="YY" required>
            </div>
          </div>
          
          <div class="form-group">
            <label for="card_cvv">CVV</label>
            <input type="text" id="card_cvv" name="card_cvv" placeholder="123" required>
          </div>
          
          <button type="submit" class="place-order-btn">Place Order</button>
        </form>
      </div>
      
      <div class="order-summary">
        <h3>Order Summary</h3>
        <div class="summary-row">
          <span>Subtotal</span>
          <span>₦<%= total %></span>
        </div>
        <div class="summary-row">
          <span>Shipping</span>
          <span>Free</span>
        </div>
        <div class="summary-row total">
          <span>Total</span>
          <span>₦<%= total %></span>
        </div>
        <a href="/user/shoppingcart" class="back-to-cart">Back to Cart</a>
      </div>
    </div>

    <footer>
      <p>&copy; <%= new Date().getFullYear() %> Danilo Store. All rights reserved.</p>
    </footer>
  </body>
</html>