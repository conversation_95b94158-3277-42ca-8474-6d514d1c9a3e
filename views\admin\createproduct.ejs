<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/admin/createproduct.css" />
    <title>Document</title>
  </head>
  <body>
    <header>
      <h2>Admin</h2>
      <a href="/admin/homepage" class="dashboard-btn">Back to Dashboard</a>
    </header>
    <div class="server-msg">
      <% if(hasErr){%> <% errMsg.forEach(message =>{ %>
      <p class="error"><%= message%></p>
      <% })%> <% } %> <% if(hasSuccess){%> <% successMsg.forEach(message =>{ %>
      <p class="success"><%= message%></p>
      <% })%> <% } %>
    </div>
    <main>
      <h3>create new product</h3>
      <form
        action="/admin/createproduct"
        method="post"
        enctype="multipart/form-data"
      >
        <input type="text" placeholder="product name" name="name" />
        <input type="text" placeholder="category" name="category" />
        <input type="text" placeholder="discription" name="discription" />
        <input type="text" placeholder="size" name="size" />
        <input type="number" placeholder="price" name="price" />
        <input type="text" placeholder="stock" name="stock" />
        <label for="product-image"> Product image</label>
        <input
          type="file"
          id="product-image"
          name="image"
          placeholder="product image"
        />
        <input type="submit" value="create" />
      </form>
    </main>
  </body>
</html>
