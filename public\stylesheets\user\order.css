* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

header h2 a {
  color: #3a6ea5;
  text-decoration: none;
}

.header-links {
  display: flex;
  gap: 1.5rem;
}

.header-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.header-links a:hover {
  color: #3a6ea5;
}

.logout-link {
  color: #dc3545 !important;
}

.server-msg {
  max-width: 800px;
  margin: 1rem auto;
  padding: 0 1rem;
}

.server-msg .error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.server-msg .success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

main {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.orders-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.orders-container h1 {
  margin-bottom: 1.5rem;
  color: #333;
}

.no-orders {
  text-align: center;
  padding: 2rem 0;
}

.shop-now-btn {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: #3a6ea5;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s;
}

.shop-now-btn:hover {
  background-color: #2c5282;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.order-status.pending {
  background-color: #ffeeba;
  color: #856404;
}

.order-status.processing {
  background-color: #b8daff;
  color: #004085;
}

.order-status.shipped {
  background-color: #c3e6cb;
  color: #155724;
}

.order-status.delivered {
  background-color: #d4edda;
  color: #155724;
}

.order-status.cancelled {
  background-color: #f5c6cb;
  color: #721c24;
}

.order-items {
  padding: 1rem;
}

.order-item {
  display: flex;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.order-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.order-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 1rem;
}

.item-details h4 {
  margin-bottom: 0.25rem;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-top: 1px solid #ddd;
}

.order-total {
  font-weight: bold;
  font-size: 1.1rem;
}

.view-details-btn {
  padding: 0.5rem 1rem;
  background-color: #3a6ea5;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: background-color 0.3s;
}

.view-details-btn:hover {
  background-color: #2c5282;
}