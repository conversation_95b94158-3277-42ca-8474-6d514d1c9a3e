/* dashboard.css */

/* Reset basic styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f4f6f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #333;
  height: 100vh;
}

/* Header Styling */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #333;
  color: white;
}

header h2 {
  margin: 0;
  font-size: 1.5rem;
}

/* Main Content Styling */
main {
  margin-top: 2rem;
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

main h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

/* Dashboard Links Styling */
main a {
  display: block;
  width: 100%;
  text-align: center;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  color: #fff;
  background-color: #333;
  text-decoration: none;
  font-size: 1rem;
  border-radius: 5px;
  transition: background-color 0.3s;
}

main a:hover {
  background-color: #555;
}

/* Responsive Design */
@media (max-width: 600px) {
  main {
    width: 100%;
  }
}

/* Add styles for the logout button */
.logout-btn {
  background-color: #dc3545;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
}

.logout-btn:hover {
  background-color: #c82333;
}

/* Improve the styling of admin links */
.admin-links {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.admin-link {
  display: block;
  background-color: #3a6ea5;
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  text-align: center;
  font-weight: bold;
  transition: background-color 0.3s, transform 0.2s;
}

.admin-link:hover {
  background-color: #2c5282;
  transform: translateY(-3px);
}
