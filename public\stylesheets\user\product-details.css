/* Product Details Page Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

header {
  background-color: #333;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 a {
  color: white;
  text-decoration: none;
}

.header-links {
  display: flex;
  gap: 1.5rem;
}

.header-links a {
  color: white;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
}

.header-links a:hover {
  color: #ddd;
}

.cart-link {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -12px;
  background-color: #ff6b6b;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 50%;
}

.server-msg {
  max-width: 1200px;
  margin: 1rem auto;
  padding: 0 1rem;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
  border-radius: 0.25rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
  border-radius: 0.25rem;
}

.product-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.product-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.in-stock {
  color: #28a745;
  font-weight: bold;
}

.out-of-stock {
  color: #dc3545;
  font-weight: bold;
}

.product-price {
  margin-bottom: 1.5rem;
}

.product-price h2 {
  font-size: 2rem;
  color: #ff6b6b;
}

.product-description {
  margin-bottom: 2rem;
}

.product-description h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.product-description p {
  color: #6c757d;
  line-height: 1.8;
}

.product-actions {
  margin-top: 2rem;
}

.add-to-cart-btn,
.login-btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-btn {
  background-color: #3a6ea5;
  color: white;
}

.add-to-cart-btn:hover {
  background-color: #2c5282;
  transform: translateY(-2px);
}

.login-btn {
  background-color: #ff6b6b;
  color: white;
}

.login-btn:hover {
  background-color: #e05252;
  transform: translateY(-2px);
}

/* Related Products Section */
.related-products {
  max-width: 1200px;
  margin: 3rem auto;
  padding: 0 1rem;
}

.related-products h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  text-align: center;
}

.related-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
}

.related-product {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.related-product:hover {
  transform: translateY(-5px);
}

.related-product a {
  text-decoration: none;
  color: inherit;
  display: block;
}

.related-product img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.related-product h3 {
  font-size: 1rem;
  padding: 0.8rem;
  margin: 0;
  color: #2c3e50;
}

.related-product .price {
  padding: 0 0.8rem 0.8rem;
  color: #ff6b6b;
  font-weight: bold;
  margin: 0;
}

.related-placeholder,
.no-related {
  grid-column: 1 / -1;
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

/* Server Messages */
.server-msg {
  max-width: 1200px;
  margin: 1rem auto;
  padding: 0 1rem;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
  border-radius: 0.25rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
  border-radius: 0.25rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .product-details-container {
    flex-direction: column;
  }

  .product-info {
    border-left: none;
    border-top: 1px solid #eee;
  }
}
