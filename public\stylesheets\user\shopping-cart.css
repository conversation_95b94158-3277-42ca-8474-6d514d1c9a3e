/* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

/* Header Styling */
header {
  background-color: #333;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 a {
  color: white;
  text-decoration: none;
}

header h2 {
  font-size: 1.2rem;
  font-weight: normal;
}

.server-msg {
  max-width: 1200px;
  margin: 1rem auto;
  padding: 0 1rem;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
  border-radius: 0.25rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
  border-radius: 0.25rem;
}

/* Shopping Cart Container */
.shoppingcart-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.empty-cart {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-cart h4 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #6c757d;
}

.continue-shopping {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  background-color: #3a6ea5;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.continue-shopping:hover {
  background-color: #2c5282;
}

.cart-items {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.cart-items h3 {
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.cart-item {
  display: grid;
  grid-template-columns: 100px 1fr auto;
  gap: 1.5rem;
  padding: 1.5rem 0;
  border-bottom: 1px solid #eee;
}

.item-image img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  object-fit: cover;
}

.item-details h3 {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  border: none;
  padding: 0;
}

.item-category {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.item-quantity {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quantity-actions {
  display: flex;
  gap: 0.5rem;
}

.reduce-btn,
.add-btn,
.remove-btn {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
}

.reduce-btn,
.add-btn {
  background-color: #e9ecef;
  color: #333;
}

.remove-btn {
  background-color: #f8d7da;
  color: #721c24;
}

.item-price h4 {
  font-size: 1.2rem;
  color: #ff6b6b;
}

.cart-summary {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  align-self: start;
}

.cart-summary h3 {
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.summary-row.total {
  font-weight: bold;
  font-size: 1.2rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.checkout-btn {
  display: block;
  width: 100%;
  padding: 0.8rem 0;
  background-color: #28a745;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 1rem;
  transition: background-color 0.3s;
}

.checkout-btn:hover {
  background-color: #218838;
}

.continue-shopping {
  display: block;
  width: 100%;
  padding: 0.8rem 0;
  background-color: #3a6ea5;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
  transition: background-color 0.3s;
}

/* Responsive Design */
@media (max-width: 768px) {
  .shoppingcart-container {
    grid-template-columns: 1fr;
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
  }

  .item-price {
    grid-column: 1 / -1;
    text-align: right;
    padding-top: 1rem;
  }
}

footer {
  background-color: #333;
  color: white;
  text-align: center;
  padding: 1.5rem;
  margin-top: 3rem;
}
