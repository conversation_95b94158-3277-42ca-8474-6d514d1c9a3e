/* create-admin.css */

/* Reset basic styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f4f6f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #333;
  height: 100vh;
}

/* Header Styling */
header {
  background-color: #333;
  color: #fff;
  padding: 1rem 2rem;
  width: 100%;
  text-align: center;
  font-size: 1.8rem;
  font-weight: bold;
  letter-spacing: 1px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h2 {
  margin: 0;
}

/* Dashboard Button Styling */
.dashboard-btn {
  background-color: #3a6ea5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
}

.dashboard-btn:hover {
  background-color: #2c5282;
}

/* Server Message Styling */
.server-msg {
  margin-top: 1rem;
  width: 90%;
  max-width: 500px;
}

.server-msg .error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 5px;
  font-weight: bold;
}

.server-msg .success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 5px;
  font-weight: bold;
}

/* Main Content Styling */
main {
  margin-top: 2rem;
  width: 90%;
  max-width: 500px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Main Heading */
main h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1.5rem;
}

/* Form Styling */
form {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1rem;
}

input[type="text"],
input[type="email"],
input[type="password"] {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 1rem;
  width: 100%;
}

input[type="submit"] {
  background-color: #333;
  color: #fff;
  font-size: 1rem;
  padding: 0.75rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

input[type="submit"]:hover {
  background-color: #555;
}

/* Responsive Design */
@media (max-width: 500px) {
  main,
  .server-msg {
    width: 100%;
  }
}
