/* styles.css */

/* Reset some default styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f4f6f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #333;
}

/* Header Styling */
header {
  background-color: #333;
  color: #fff;
  padding: 1rem;
  width: 100%;
  text-align: center;
  font-size: 1.5rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Server Message Styling */
.server-msg {
  margin-top: 1rem;
  width: 90%;
  max-width: 500px;
}

.server-msg p {
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 5px;
  font-weight: bold;
}

.server-msg p:nth-child(odd) {
  background-color: #f8d7da;
  color: #721c24;
}

.server-msg p:nth-child(even) {
  background-color: #d4edda;
  color: #155724;
}

/* Main Content Styling */
main {
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-top: 2rem;
  width: 90%;
  max-width: 500px;
  border-radius: 8px;
}

/* Welcome Text */
main h4 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Form Styling */
form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

input[type="email"],
input[type="password"] {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 1rem;
}

input[type="submit"] {
  background-color: #333;
  color: #fff;
  font-size: 1rem;
  padding: 0.75rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

input[type="submit"]:hover {
  background-color: #555;
}

/* Responsive Design */
@media (max-width: 500px) {
  main,
  .server-msg {
    width: 100%;
  }
}
