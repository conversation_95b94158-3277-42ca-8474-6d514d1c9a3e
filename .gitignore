# Node modules
node_modules/

# Environment variables
.env

# Log files
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Mac files
.DS_Store

# Windows files
Thumbs.db
ehthumbs.db
Desktop.ini

# VS Code files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Uploaded files (if applicable)
uploads/*
!uploads/.gitkeep

# Build directories
dist/
build/
 
