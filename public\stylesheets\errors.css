* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.error-container {
  max-width: 600px;
  text-align: center;
  background-color: white;
  padding: 3rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-code {
  font-size: 8rem;
  font-weight: 700;
  color: #3a6ea5;
  line-height: 1;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

p {
  margin-bottom: 1.5rem;
  color: #6c757d;
}

.error-details {
  margin: 2rem 0;
  text-align: left;
}

.error-message {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  color: #dc3545;
  font-weight: 500;
}

.error-stack {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  color: #6c757d;
  font-size: 0.9rem;
  overflow-x: auto;
  white-space: pre-wrap;
  margin-top: 1rem;
}

.btn-home {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  background-color: #3a6ea5;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-home:hover {
  background-color: #2c5282;
  transform: translateY(-2px);
}