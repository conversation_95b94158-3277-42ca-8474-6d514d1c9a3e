* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

header h2 a {
  color: #3a6ea5;
  text-decoration: none;
}

.header-links {
  display: flex;
  gap: 1.5rem;
}

.header-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.header-links a:hover {
  color: #3a6ea5;
}

.logout-link {
  color: #dc3545 !important;
}

.server-msg {
  max-width: 1000px;
  margin: 1rem auto;
  padding: 0 1rem;
}

.server-msg .error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.server-msg .success {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

main {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.order-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.order-header h1 {
  margin-bottom: 0.5rem;
  color: #333;
}

.order-date {
  color: #6c757d;
}

.order-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.order-status.pending {
  background-color: #ffeeba;
  color: #856404;
}

.order-status.processing {
  background-color: #b8daff;
  color: #004085;
}

.order-status.shipped {
  background-color: #c3e6cb;
  color: #155724;
}

.order-status.delivered {
  background-color: #d4edda;
  color: #155724;
}

.order-status.cancelled {
  background-color: #f5c6cb;
  color: #721c24;
}

.order-sections {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 2rem;
  padding: 2rem;
}

.order-details-section h2,
.order-info-section h2 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.3rem;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-item {
  display: flex;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.order-item:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.item-image {
  width: 100px;
  height: 100px;
  margin-right: 1.5rem;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.item-details h3 {
  margin-bottom: 0.5rem;
}

.item-category {
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.item-quantity,
.item-price {
  margin-bottom: 0.25rem;
}

.shipping-info,
.payment-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.shipping-info p {
  margin-bottom: 0.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #eee;
}

.summary-row.total {
  font-weight: bold;
  font-size: 1.1rem;
  margin-top: 1rem;
  border-bottom: none;
}

.payment-method {
  margin-top: 1rem;
  font-style: italic;
  color: #6c757d;
}

.order-actions {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background-color: #f8f9fa;
  border-top: 1px solid #ddd;
}

.back-btn,
.cancel-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s;
}

.back-btn {
  background-color: #6c757d;
  color: white;
}

.back-btn:hover {
  background-color: #5a6268;
}

.cancel-btn {
  background-color: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background-color: #c82333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .order-sections {
    grid-template-columns: 1fr;
  }
  
  .order-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .order-status {
    margin-top: 1rem;
  }
  
  .order-item {
    flex-direction: column;
  }
  
  .item-image {
    width: 100%;
    height: 200px;
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .order-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .back-btn,
  .cancel-btn {
    text-align: center;
  }
}